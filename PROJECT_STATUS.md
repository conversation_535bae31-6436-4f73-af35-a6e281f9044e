# TeleStore Project Status & Documentation

## 📊 Tổng quan dự án

TeleStore là hệ thống quản lý file cá nhân sử dụng Telegram làm kho lưu trữ đám mây miễn phí với giao diện web trực quan tương tự Google Drive/Dropbox.

## ✅ Tính năng đã hoàn thành

### 🔐 Hệ thống xác thực (Authentication)
- **Traditional Login**: Đ<PERSON><PERSON> nhập bằng username/password ✅
- **Google OAuth**: Đăng nhập bằng tài khoản Google ✅
- **Telegram Login**: Đăng nhập bằng tài khoản Telegram ✅
- **JWT Token Management**: Quản lý token với Redis ✅
- **User Profile Management**: Quản lý thông tin người dùng ✅
- **Email Verification**: <PERSON>á<PERSON> thực email cho tài khoản local ✅
- **OAuth Toggle**: Bật/tắt OAuth features trong config ✅

### 📁 Quản lý File & Folder
- **File Upload**: Upload đa file với progress bar ✅
- **Folder Management**: <PERSON><PERSON><PERSON>, đổi tên, xóa thư mục ✅
- **File Download**: Tải file từ Telegram ✅
- **File/Folder Rename**: Đổi tên file và thư mục ✅
- **File/Folder Delete**: Xóa file và thư mục với duplicate key handling ✅
- **Breadcrumb Navigation**: Điều hướng qua các thư mục ✅
- **Search Functionality**: Tìm kiếm file và thư mục ✅
- **Context Menu**: Menu chuột phải cho file/folder ✅

### 🎨 Giao diện người dùng (UI/UX)
- **Responsive Design**: Giao diện responsive với Material-UI ✅
- **Dark Mode**: Chế độ tối hoàn chỉnh ✅
- **Dropbox-inspired Interface**: Thiết kế lấy cảm hứng từ Dropbox ✅
- **File Preview Thumbnails**: Hiển thị thumbnail cho ảnh ✅
- **Upload Queue Preview**: Xem trước file trong queue upload ✅
- **Loading States**: Trạng thái loading cho các thao tác ✅
- **Beautiful UI**: Giao diện đẹp, thân thiện người dùng ✅

### 🔧 Hệ thống Backend
- **Node.js/Express**: API server với Express framework ✅
- **MongoDB**: Database với user-specific data ✅
- **Redis**: Caching và session management ✅
- **Telegram Bot API**: Tích hợp với Telegram để lưu trữ ✅
- **File Type Support**: Hỗ trợ tất cả loại file (không chỉ ảnh/video) ✅
- **Security**: CSRF protection, input validation, XSS protection ✅
- **Error Handling**: Xử lý lỗi toàn diện ✅

### 📱 File Preview System
- **Image Preview**: Xem trước ảnh trực tiếp ✅
- **Video Preview**: Xem trước video với controls ✅
- **Audio Preview**: Xem trước audio với controls ✅
- **Text File Preview**: Xem trước file text với syntax highlighting ✅
- **Single-click Preview**: Preview bằng 1 click thay vì double-click ✅

## ⚠️ Lỗi hiện tại cần sửa

### 🔴 PDF Preview Issues
- **PDF không hiển thị**: PDF files không load được trong iframe
- **Loading state**: PDF loading indicator không hoạt động đúng
- **Error handling**: Xử lý lỗi PDF chưa tối ưu
- **Browser compatibility**: Một số browser không hỗ trợ PDF preview

### 🔴 Office File Preview Issues
- **Google Docs Viewer**: Google Docs viewer không load được file từ Telegram URL
- **CORS Issues**: Vấn đề CORS khi embed Office files
- **Authentication**: Google viewer không thể access file cần authentication
- **Fallback mechanism**: Chưa có fallback tốt khi preview thất bại

### 🔴 Authentication & Security
- **Token expiration**: Chưa handle token expiration gracefully
- **Session management**: Session cleanup chưa tối ưu
- **OAuth error handling**: Error handling cho OAuth flow chưa hoàn thiện

### 🔴 File Management
- **Large file handling**: Upload file lớn (>20MB) có thể timeout
- **Progress tracking**: Progress bar không accurate cho file lớn
- **Concurrent uploads**: Upload nhiều file cùng lúc có thể gây lỗi

## ✅ Tính năng đã hoàn thành gần đây

### 🎉 Office File Preview System (Hoàn thành 18/07/2025)
- **Google Docs Viewer Integration**: Tích hợp Google Docs Viewer làm primary viewer
- **Microsoft Office Online Fallback**: Thêm Microsoft Office Online làm fallback viewer
- **Enhanced Error Handling**: Xử lý lỗi toàn diện với thông báo rõ ràng
- **Alternative Viewer Switching**: Tự động chuyển đổi viewer khi gặp lỗi
- **Improved CORS Support**: Cải thiện CORS headers cho iframe embedding
- **Better File Type Detection**: Phát hiện loại file chính xác hơn với icons phù hợp
- **Loading States**: Loading indicators và feedback tốt hơn
- **Troubleshooting Guide**: Hướng dẫn xử lý sự cố cho người dùng
- **SimpleOfficePreview Component**: Component preview đơn giản và ổn định
- **Backend File Info API**: API mới để lấy thông tin chi tiết file

**Các file được cải thiện:**
- `frontend/src/components/SimpleOfficePreview.tsx` (mới)
- `frontend/src/components/OfficePreviewHelper.tsx` (mới)
- `backend/lib/routes/api/files/info/v1.0.js` (mới)
- `backend/lib/routes/api/files/preview/v1.0.js` (cải thiện CORS)
- `frontend/src/services/api.ts` (thêm getFileInfo API)

**Technical Improvements:**
- **Multiple Viewer Support**: Hỗ trợ nhiều viewer với fallback mechanism
- **Enhanced CORS Policy**: Cải thiện CORS headers để hỗ trợ external viewers
- **Error Recovery**: Tự động thử alternative viewer khi primary viewer fail
- **User Experience**: Loading states, error messages, và troubleshooting tips
- **Component Architecture**: Tách biệt logic preview thành components riêng biệt
- **API Enhancement**: Thêm file info API để cung cấp metadata chi tiết

## 🚧 Tính năng chưa hoàn thành

### 📋 Cần làm tiếp
- [ ] **Fix PDF Preview**: Sửa lỗi PDF không hiển thị trong browser
- [x] **Fix Office Preview**: ✅ **HOÀN THÀNH** - Đã sửa lỗi Office file preview với Google Docs Viewer và Microsoft Office Online
- [x] **Improve Error Handling**: ✅ **HOÀN THÀNH** - Đã cải thiện xử lý lỗi cho preview system với fallback options
- [ ] **File Sharing**: Chia sẻ file/folder với người khác
- [ ] **File Versioning**: Quản lý phiên bản file
- [ ] **Bulk Operations**: Thao tác hàng loạt (select multiple, bulk delete)
- [ ] **Advanced Search**: Tìm kiếm nâng cao (theo loại file, kích thước, ngày)
- [ ] **File Tags**: Gắn tag cho file để tổ chức tốt hơn
- [ ] **Trash/Recycle Bin**: Thùng rác để khôi phục file đã xóa
- [ ] **File Compression**: Nén file trước khi upload
- [ ] **Duplicate Detection**: Phát hiện file trùng lặp

### 🔮 Tính năng tương lai
- [ ] **Mobile App**: Ứng dụng mobile React Native
- [ ] **Desktop App**: Ứng dụng desktop với Electron
- [ ] **Auto Sync**: Đồng bộ tự động từ điện thoại/máy tính
- [ ] **AI Integration**: Tích hợp AI để auto-tag và mô tả file
- [ ] **Local Bot API**: Hỗ trợ file >50MB với Local Bot API Server
- [ ] **Multi-user Management**: Quản lý đa người dùng với roles
- [ ] **Backup & Restore**: Sao lưu và khôi phục dữ liệu
- [ ] **API Documentation**: Tài liệu API đầy đủ với Swagger

## 🏗️ Kiến trúc hệ thống

### Backend Structure
```
backend/
├── lib/
│   ├── models/          # MongoDB models (User, File, Folder)
│   ├── routes/          # API routes (auth, files, folders, browse)
│   ├── services/        # Business logic (telegram, cache, email)
│   ├── middleware/      # Express middleware (auth, upload, error)
│   └── connections/     # Database connections (MongoDB, Redis)
├── config/              # Configuration files
└── index.js            # Main server file
```

### Frontend Structure
```
frontend/src/
├── components/          # React components
│   ├── FileGrid.tsx    # Main file grid with preview
│   ├── Header.tsx      # Navigation header
│   ├── LoginPage.tsx   # Authentication page
│   └── ...
├── services/           # API services
├── contexts/           # React contexts (Auth, Theme)
├── types/              # TypeScript types
└── utils/              # Utility functions
```

## 🔧 Cấu hình hiện tại

### Environment Variables
```env
# Telegram Configuration
TELEGRAM_BOT_TOKEN=configured ✅
TELEGRAM_CHAT_ID=configured ✅

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/tele-store ✅
REDIS_HOST=localhost ✅
REDIS_PORT=6379 ✅

# OAuth Configuration
GOOGLE_CLIENT_ID=configured ✅
GOOGLE_CLIENT_SECRET=configured ✅
TELEGRAM_BOT_USERNAME=configured ✅

# Application Configuration
NODE_ENV=development ✅
PORT=3000 ✅
SECRET_KEY=configured ✅
```

### File Upload Limits
- **Max file size**: 50MB (Telegram Bot API limit)
- **Supported file types**: All file types (không giới hạn extension)
- **Concurrent uploads**: 10 files maximum
- **Blocked extensions**: .exe, .bat, .cmd, .scr (security reasons)

## 📊 Database Schema

### User Model
```javascript
{
  _id: ObjectId,
  username: String,
  email: String,
  password: String (hashed),
  provider: String, // 'local', 'google', 'telegram'
  providerId: String,
  isEmailVerified: Boolean,
  role: String,
  status: Number,
  createdAt: Date,
  updatedAt: Date
}
```

### File Model
```javascript
{
  _id: ObjectId,
  telegramFileId: String,
  originalFileName: String,
  fileSize: Number,
  mimeType: String,
  parentId: ObjectId, // folder ID
  ownerId: ObjectId, // user ID
  uploadDate: Date,
  isDeleted: Boolean
}
```

### Folder Model
```javascript
{
  _id: ObjectId,
  folderName: String,
  parentId: ObjectId,
  ownerId: ObjectId, // user ID
  createdAt: Date,
  isDeleted: Boolean
}
```

## 🚀 Deployment

### Development
```bash
# Backend
cd backend && npm start

# Frontend
cd frontend && npm start
```

### Production (Docker)
```bash
docker-compose up -d
```

## 📝 Notes cho Developer

### Khi thêm tính năng mới:
1. **Backend**: Thêm route trong `backend/lib/routes/api/`
2. **Frontend**: Thêm component trong `frontend/src/components/`
3. **API Service**: Cập nhật `frontend/src/services/api.ts`
4. **Types**: Cập nhật TypeScript types trong `frontend/src/types/`
5. **Update this file**: Cập nhật PROJECT_STATUS.md

### Khi fix bug:
1. **Identify**: Xác định root cause của bug
2. **Test**: Viết test case để reproduce bug
3. **Fix**: Implement fix
4. **Verify**: Verify fix hoạt động
5. **Update**: Cập nhật status trong file này

### Priority cho việc fix:
1. **High**: PDF và Office file preview (ảnh hưởng UX chính)
2. **Medium**: Authentication improvements
3. **Low**: Performance optimizations

---

**Last Updated**: 2025-01-17
**Version**: 1.0.0
**Status**: In Development
